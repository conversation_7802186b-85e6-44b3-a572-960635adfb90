using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Data;
using System.Runtime.Serialization;

namespace NextBroker.Pages.Polisi
{
   
    public class DodajPolisaKlasa4Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public enum TipNaFakturaEnum
        {
            [Display(Name = "Влезна фактура кон клиент")]
            [EnumMember(Value = "Влезна фактура кон клиент")]
            VleznaFakturaKonKlient,
            
            [Display(Name = "Влезна фактура кон брокер")]
            [EnumMember(Value = "Влезна фактура кон брокер")]
            VleznaFakturaKonBroker
        }

        public DodajPolisaKlasa4Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }
        [BindProperty]
        public PolisaInputModel Input { get; set; } = new();

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> Valuti { get; set; }
        public IEnumerable<SelectListItem> NaciniNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> TipoviNaFaktura { get; set; }
        public IEnumerable<SelectListItem> TipoviNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> Banki { get; set; }
        public IEnumerable<SelectListItem> PriciniZaStorno { get; set; }
        public IEnumerable<SelectListItem> TipoviNaVozilo { get; set; }

     public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("DodajPolisaKlasa4"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await LoadProdukti();
            await LoadValuti();
            await LoadNaciniNaPlakanje();
            LoadTipoviNaFaktura();
            await LoadTipoviNaPlakanje();
            await LoadBanki();
            await LoadPriciniZaStorno();
            await LoadTipoviNaVozilo();
            return Page();
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    AND ZivotNezivot = N'Неживот'
                    AND DogovorVaziDo > Convert(date,GETDATE())
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(KlasaBroj, ' - ', KlasaIme) as DisplayName 
                    FROM KlasiOsiguruvanje 
                    WHERE (Disabled = 0 OR Disabled IS NULL )
                    AND Id = '4'
                    ORDER BY KlasaBroj", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task LoadProdukti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime 
                    FROM Produkti
                    WHERE iD in ('44')
                    ORDER BY Ime", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Ime"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Produkti = items;
                }
            }
        }

        private async Task LoadValuti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(Valuta, ' - ', ImeValuta) as DisplayName 
                    FROM SifrarnikValuti 
                    ORDER BY Valuta", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Valuti = items;
                }
            }
        }

        private async Task LoadNaciniNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, NacinNaPlakanje 
                    FROM SifrarnikNacinNaPlakanje 
                    WHERE Id not in ('2','3','4')
                    ORDER BY NacinNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["NacinNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    NaciniNaPlakanje = items;
                }
            }
        }

        private void LoadTipoviNaFaktura()
        {
            TipoviNaFaktura = Enum.GetValues(typeof(TipNaFakturaEnum))
                .Cast<TipNaFakturaEnum>()
                .Select(e => new SelectListItem
                {
                    Text = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<DisplayAttribute>()
                        ?.Name ?? e.ToString(),
                    Value = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<EnumMemberAttribute>()
                        ?.Value ?? e.ToString()
                })
                .ToList();
        }

        private async Task LoadTipoviNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaPlakanje 
                    FROM SifrarnikTipNaPlakanje 
                    ORDER BY TipNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaPlakanje = items;
                }
            }
        }

        private async Task LoadBanki()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Banka 
                    FROM SifrarnikBanki 
                    ORDER BY Banka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }
            }
        }

        private async Task LoadPriciniZaStorno()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, PricinaZaStorno 
                    FROM SifrarnikPricinaZaStorno 
                    ORDER BY PricinaZaStorno", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["PricinaZaStorno"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    PriciniZaStorno = items;
                }
            }
        }

        private async Task LoadTipoviNaVozilo()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaVozilo 
                    FROM SifrarnikTipNaVozilo 
                    ORDER BY TipNaVozilo", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaVozilo"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaVozilo = items;
                }
            }
        }

        public class PolisaInputModel
        {
            [Required(ErrorMessage = "Осигурител е задолжително поле")]
            [Display(Name = "Осигурител")]
            public long KlientiIdOsiguritel { get; set; }

            [Required(ErrorMessage = "Класа на осигурување е задолжително поле")]
            [Display(Name = "Класа на осигурување")]
            public int KlasiOsiguruvanjeIdKlasa { get; set; }

            [Display(Name = "Продукт")]
            public int? ProduktiIdProizvod { get; set; }

            [Required(ErrorMessage = "Број на полиса е задолжително поле")]
            [Display(Name = "Број на полиса")]
            public string? BrojNaPolisa { get; set; }

            [Display(Name = "Број на понуда")]
            public long? BrojNaPonuda { get; set; }

            [Required(ErrorMessage = "Договорувач е задолжително поле")]
            [Display(Name = "Договорувач")]
            public long KlientiIdDogovoruvac { get; set; }

            [Display(Name = "Осигуреник")]
            [Required(ErrorMessage = "Осигуреник е задолжително поле")]
            public long? KlientiIdOsigurenik { get; set; }

            [Display(Name = "Колективна")]
            public bool Kolektivna { get; set; }

            [Display(Name = "Колективна со неодреден број на осигуреници")]
            public bool KolektivnaNeodredenBrOsigurenici { get; set; }

            [Display(Name = "Забелешка за неодреден број осигуреници")]
            public string? NeodredenBrOsigureniciZabeleska { get; set; }

            [Required(ErrorMessage = "Важи од е задолжително поле")]
            [Display(Name = "Важи од")]
            public DateTime? DatumVaziOd { get; set; }

            [Required(ErrorMessage = "Важи до е задолжително поле")]
            [Display(Name = "Важи до")]
            [DateGreaterThan("DatumVaziOd", ErrorMessage = "Датумот 'Важи до' мора да биде после датумот 'Важи од'")]
            public DateTime? DatumVaziDo { get; set; }

            [Required(ErrorMessage = "Датум на издавање е задолжително поле")]
            [Display(Name = "Датум на издавање")]
            public DateTime? DatumNaIzdavanje { get; set; }

            [Display(Name = "Времетраење на полиса")]
            public int? VremetraenjeNaPolisa { get; set; }

            [Display(Name = "Период на уплата")]
            public int? PeriodNaUplata { get; set; }

            [Required(ErrorMessage = "Валута е задолжително поле")]
            [Display(Name = "Валута")]
            public long SifrarnikValutiIdValuta { get; set; }

            [Display(Name = "Соработник")]
            public long? KlientiIdSorabotnik { get; set; }

            [Display(Name = "Факторинг")]
            public bool Faktoring { get; set; }

            [Display(Name = "Основна премија")]
            public decimal? OsnovnaPremija { get; set; }

            [Display(Name = "Дополнителна премија")]
            public decimal? DopolnitelnaPremija { get; set; }

            [Display(Name = "Вкупна премија")]
            public decimal? VkupnaPremija { get; set; }

            [Display(Name = "Процент на попуст за фактура во рок")]
            public decimal? ProcentNaPopustZaFakturaVoRok { get; set; }

            [Display(Name = "Износ за плаќање во рок")]
            public decimal? IznosZaPlakjanjeVoRok { get; set; }

            [Display(Name = "Процент комерцијален попуст")]
            public decimal? ProcentKomercijalenPopust { get; set; }

            [Display(Name = "Процент финансиски")]
            public decimal? ProcentFinansiski { get; set; }

            [Display(Name = "Премија за наплата")]
            public decimal? PremijaZaNaplata { get; set; }

            [Display(Name = "Уплатено")]
            public decimal? Uplateno { get; set; }

            [Display(Name = "Должна премија")]
            public decimal? DolznaPremija { get; set; }

            [Display(Name = "Корегирана стапка на провизија")]
            [Range(0, 100)]
            public decimal? KoregiranaStapkaNaProvizija { get; set; }

            [Required(ErrorMessage = "Начин на плаќање е задолжително поле")]
            [Display(Name = "Начин на плаќање")]
            public long SifrarnikNacinNaPlakjanjeId { get; set; }

            [Display(Name = "Тип на фактура")]
            public string TipNaFaktura { get; set; }
            [Display(Name = "Број на влезна фактура")]
            public string? BrojNaFakturaVlezna { get; set; }
            [Display(Name = "Датум на влезна фактура")]
            public DateTime? DatumNaFakturaVlezna { get; set; }
            [Display(Name = "Рок на плаќање влезна фактура")]
            public DateTime? RokNaPlakjanjeFakturaVlezna { get; set; }
            public long? SifrarnikTipNaPlakanjeId { get; set; }
            public int? SifrarnikBankiIdBanka { get; set; }
            public bool GeneriranaFakturaIzlezna { get; set; }
            [Display(Name = "Број на излезна фактура")]
            public string? BrojNaFakturaIzlezna { get; set; }
            [Display(Name = "Датум на излезна фактура")]
            public DateTime? DatumNaIzleznaFaktura { get; set; }
            [Display(Name = "Рок на плаќање излезна фактура")]
            public DateTime? RokNaPlakjanjeFakturaIzlezna { get; set; }
            public bool Storno { get; set; }
            public long? SifrarnikPricinaZaStornoId { get; set; }
            public string? Zabeleska { get; set; }

            [Display(Name = "Процент на попуст за фактура во рок АО")]
            public decimal? ProcentNaPopustZaFakturaVoRokAO { get; set; }

            [Display(Name = "Износ за плаќање во рок АО")]
            public decimal? IznosZaPlakjanjeVoRokAO { get; set; }

            [Display(Name = "Процент комерцијален попуст АО")]
            public decimal? ProcentKomercijalenPopustAO { get; set; }

            [Display(Name = "Процент финансиски попуст АО")]
            public decimal? ProcentFinansiskiPopustAO { get; set; }

            [Display(Name = "Премија за наплата АО")]
            public decimal? PremijaZaNaplataAO { get; set; }

            [Display(Name = "Уплатено АО")]
            public decimal? UplatenoAO { get; set; }

            [Display(Name = "Должна премија АО")]
            public decimal? DolznaPremijaAO { get; set; }

            [Display(Name = "Регистарска ознака")]
            public string? RegisterskaOznaka { get; set; }

            [Display(Name = "Марка")]
            public string? Marka { get; set; }

            [Display(Name = "Тип на возило")]
            public long? SifrarnikTipNaVoziloId { get; set; }

            [Display(Name = "Комерцијална ознака")]
            public string? KomercijalnaOznaka { get; set; }

            [Display(Name = "Број на шасија")]
            public string? Shasija { get; set; }

            [Display(Name = "Година на производство")]
            public int? GodinaNaProizvodstvo { get; set; }

            [Display(Name = "Зафатнина на моторот (cm³)")]
            public int? ZafatninaNaMotorotcm3 { get; set; }

            [Display(Name = "Силина на моторот (kW)")]
            public int? SilinaNaMotorotKW { get; set; }

            [Display(Name = "Број на седишта")]
            public int? BrojNaSedista { get; set; }

            [Display(Name = "Носивост (kg)")]
            public int? NosivostKG { get; set; }

            [Display(Name = "Боја на возилото")]
            public string? BojaNaVoziloto { get; set; }

            [Display(Name = "Датум на регистрација")]
            public DateTime? DatumNaRegistracija { get; set; }

            [Display(Name = "Датум на првата регистрација")]
            public DateTime? DatumNaPrvataRegistracija { get; set; }

            [Display(Name = "Број на впис")]
            public string? BrojNaVpisot { get; set; }

            [Display(Name = "Презиме/Назив на корисникот")]
            public string? PrezimeNazivNaKorisnikot { get; set; }

            [Display(Name = "Име")]
            public string? Ime { get; set; }

            [Display(Name = "Адреса на постојано живеалиште")]
            public string? AdresaNaPostojanoZivealiste { get; set; }

            [Display(Name = "ЕМБ на корисникот")]
            public string? EMBNaKorisnikot { get; set; }

            [Display(Name = "Датум на прва регистрација во РСМ")]
            public string? DatumNaPrvaRegistracijaVoRSM { get; set; }

            [Display(Name = "Дозволата ја издал")]
            public string? DozvolataJaIzdal { get; set; }

            [Display(Name = "Ознака на одобрение")]
            public string? OznakaNaOdobrenie { get; set; }

            [Display(Name = "Број на ЕУ потврда за сообразност")]
            public string? BrojNAEUPotvrdaZaSoobraznost { get; set; }

            [Display(Name = "Презиме/Назив на сопственикот")]
            public string? PrezimeNazivNaSopstvenikot { get; set; }

            [Display(Name = "Име")]
            public string? ImeSopstvenik { get; set; }

            [Display(Name = "Адреса на постојано живеалиште/седиште")]
            public string? AdresaNaPostojanoZivealisteSediste { get; set; }

            [Display(Name = "ЕМБ на физичко/правно лице")]
            public string? EMBNaFizickoLiceEMBNaPravnoLice { get; set; }

            [Display(Name = "Категорија и вид на возилото")]
            public string? KategorijaIVidNaVoziloto { get; set; }

            [Display(Name = "Облик и намена на каросеријата")]
            public string? OblikINamenaNaKaroserijata { get; set; }

            [Display(Name = "Тип на моторот")]
            public string? TipNaMotorot { get; set; }

            [Display(Name = "Вид на гориво")]
            public string? VidNaGorivo { get; set; }

            [Display(Name = "Број на вртежи")]
            public string? BrojNaVrtezi { get; set; }

            [Display(Name = "Идентификационен број на моторот")]
            public string? IdentifikacionenBrojNaMotorot { get; set; }

            [Display(Name = "Максимална брзина (km/h)")]
            public string? MaksimalnaBrzinaKM { get; set; }

            [Display(Name = "Однос сила/маса")]
            public string? OdnosSilinaMasa { get; set; }

            [Display(Name = "Маса на возилото")]
            public string? MasaNaVoziloto { get; set; }

            [Display(Name = "Најголема конструктивна маса (kg)")]
            public string? NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG { get; set; }

            [Display(Name = "Најголема легална маса (kg)")]
            public string? NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG { get; set; }

            [Display(Name = "Најголема легална маса на група (kg)")]
            public string? NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG { get; set; }

            [Display(Name = "Валута за франшиза")]
            public long? SifrarnikValutiIdFranshizaValuta { get; set; }

            [Display(Name = "Процент франшиза")]
            [Range(0, 100)]
            public decimal? ProcentFranshiza { get; set; }

            [Display(Name = "Осигурување на патници")]
            public bool KlasiOsiguruvanjeKlasa1 { get; set; }

            [Display(Name = "Осигурување на стакло")]
            public bool KlasiOsiguruvanjeKlasa8 { get; set; }

            [Display(Name = "Број на оски")]
            public string? BrojNaOski { get; set; }

            [Display(Name = "Распредеба на најголемата конструктивна маса по оска")]
            public string? RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka { get; set; }

            [Display(Name = "Најголемо конструктивно основно оптивување")]
            public string? NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka { get; set; }

            [Display(Name = "Должина")]
            public int? Dolzhina { get; set; }

            [Display(Name = "Висина")]
            public int? Visina { get; set; }

            [Display(Name = "Најголема конструктивна маса на коцен на приколка")]
            public string? NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG { get; set; }

            [Display(Name = "Најголема конструктивна маса на неекоцен приколка")]
            public string? NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG { get; set; }

            [Display(Name = "Број на места за стоење")]
            public int? BrojNaMestaZaStoenje { get; set; }

            [Display(Name = "Дозволени пневматици и наплатки")]
            public string? DozvoleniPnevmaticiINaplatki { get; set; }

            [Display(Name = "Број на места за лезење")]
            public int? BrojNaMestazaLezenje { get; set; }

            [Display(Name = "CO2")]
            public string? CO2 { get; set; }

            [Display(Name = "Најголемо конструктивно оптивување на приклокот за приколка")]
            public string? NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka { get; set; }

            [Display(Name = "Стационарна бучавост")]
            public string? StacionarnaBucavost { get; set; }

            [Display(Name = "Вредност")]
            public decimal? Vrednost { get; set; }

            [Display(Name = "Сума на осигурување")]
            public decimal? SumaNaosiguruvanje { get; set; }

            [Display(Name = "Износ за франшиза")]
            public decimal? FranshizaIznos { get; set; }

            [Display(Name = "Опис")]
            public string? Opis { get; set; }

            [Display(Name = "Осигурена вредност")]
            public string? OsigurenaVrednost { get; set; }
        }
        public async Task<JsonResult> OnGetCheckBrojNaPolisaAsync(string brojNaPolisa)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT COUNT(*) 
                    FROM Polisi 
                    WHERE BrojNaPolisa = @BrojNaPolisa 
                    AND (Storno = 0 OR Storno IS NULL)", connection))
                {
                    cmd.Parameters.AddWithValue("@BrojNaPolisa", brojNaPolisa);
                    int count = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                    return new JsonResult(new { exists = count > 0 });
                }
            }
        }

        public async Task<JsonResult> OnPost()
        {
            if (!await HasPageAccess("DodajPolisaKlasa4"))
            {
                return new JsonResult(new { success = false, errorMessage = "Немате пристап до оваа страница." });
            }

            // Check for duplicate policy number
            if (!string.IsNullOrEmpty(Input.BrojNaPolisa))
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        SELECT COUNT(*) 
                        FROM Polisi 
                        WHERE BrojNaPolisa = @BrojNaPolisa 
                        AND (Storno = 0 OR Storno IS NULL)", connection))
                    {
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaPolisa);
                        int count = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                        if (count > 0)
                        {
                            return new JsonResult(new { 
                                success = false, 
                                errorMessage = "Веќе постои полиса со овој број која не е сторнирана!" 
                            });
                        }
                    }
                }
            }

            // Calculate VkupnaPremija before validation
            Input.VkupnaPremija = (Input.OsnovnaPremija ?? 0) + (Input.DopolnitelnaPremija ?? 0);

            if (!ModelState.IsValid)
            {
                return new JsonResult(new { 
                    success = false, 
                    errorMessage = string.Join("<br>", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage))
                });
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                return new JsonResult(new { success = false, errorMessage = "Вашата сесија е истечена. Најавете се повторно." });
            }

            try
            {
                // More detailed debug logging
                System.Diagnostics.Debug.WriteLine("=== Form Submission Debug ===");
                System.Diagnostics.Debug.WriteLine($"Form submitted at: {DateTime.Now}");
            
                System.Diagnostics.Debug.WriteLine($"ModelState.IsValid: {ModelState.IsValid}");
                if (!ModelState.IsValid)
                {
                    foreach (var modelState in ModelState)
                    {
                        System.Diagnostics.Debug.WriteLine($"Key: {modelState.Key}");
                        foreach (var error in modelState.Value.Errors)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error: {error.ErrorMessage}");
                        }
                    }
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // First insert into Polisi table
                            long polisaId;
                            using (SqlCommand cmd = new SqlCommand(@"
                                INSERT INTO Polisi (
                                    DateCreated, UsernameCreated,
                                    KlientiIdOsiguritel, KlasiOsiguruvanjeIdKlasa, ProduktiIdProizvod,
                                    BrojNaPolisa, BrojNaPonuda, KlientiIdDogovoruvac, KlientiIdOsigurenik,
                                    Kolektivna, KolektivnaNeodredenBrOsigurenici, NeodredenBrOsigureniciZabeleska,
                                    DatumVaziOd, DatumVaziDo, DatumNaIzdavanje, VremetraenjeNaPolisa,
                                    PeriodNaUplata, SifrarnikValutiIdValuta, KlientiIdSorabotnik,
                                    Faktoring, SifrarnikValutiIdFranshizaValuta, 
                                    ProcentFranshiza, FranshizaIznos, KoregiranaStapkaNaProvizija,
                                    SifrarnikNacinNaPlakjanjeId, TipNaFaktura,
                                    BrojNaFakturaVlezna, DatumNaFakturaVlezna, RokNaPlakjanjeFakturaVlezna,
                                    SifrarnikTipNaPlakanjeId, SifrarnikBankiIdBanka,
                                    GeneriranaFakturaIzlezna, BrojNaFakturaIzlezna,
                                    DatumNaIzleznaFaktura, RokNaPlakjanjeFakturaIzlezna,
                                    Storno, PricinaZaStorno, Zabeleska)
                                VALUES (
                                    GETDATE(), @UsernameCreated,
                                    @KlientiIdOsiguritel, @KlasiOsiguruvanjeIdKlasa, @ProduktiIdProizvod,
                                    @BrojNaPolisa, @BrojNaPonuda, @KlientiIdDogovoruvac, @KlientiIdOsigurenik,
                                    @Kolektivna, @KolektivnaNeodredenBrOsigurenici, @NeodredenBrOsigureniciZabeleska,
                                    @DatumVaziOd, @DatumVaziDo, @DatumNaIzdavanje, @VremetraenjeNaPolisa,
                                    @PeriodNaUplata, @SifrarnikValutiIdValuta, @KlientiIdSorabotnik,
                                    @Faktoring, @SifrarnikValutiIdFranshizaValuta,
                                    @ProcentFranshiza, @FranshizaIznos, @KoregiranaStapkaNaProvizija,
                                    @SifrarnikNacinNaPlakjanjeId, @TipNaFaktura,
                                    @BrojNaFakturaVlezna, @DatumNaFakturaVlezna, @RokNaPlakjanjeFakturaVlezna,
                                    @SifrarnikTipNaPlakanjeId, @SifrarnikBankiIdBanka,
                                    @GeneriranaFakturaIzlezna, @BrojNaFakturaIzlezna,
                                    @DatumNaIzleznaFaktura, @RokNaPlakjanjeFakturaIzlezna,
                                    @Storno, 
                                    (SELECT PricinaZaStorno FROM SifrarnikPricinaZaStorno WHERE Id = @SifrarnikPricinaZaStornoId),
                                    @Zabeleska);
                                
                                SELECT CAST(SCOPE_IDENTITY() as bigint)", connection, transaction))
                            {
                                // Set parameters for Polisi table
                                cmd.Parameters.AddWithValue("@UsernameCreated", username);
                                cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Input.KlientiIdOsiguritel);
                                cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Input.KlasiOsiguruvanjeIdKlasa);
                                cmd.Parameters.AddWithValue("@ProduktiIdProizvod", Input.ProduktiIdProizvod ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaPolisa ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaPonuda", Input.BrojNaPonuda ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@KlientiIdDogovoruvac", Input.KlientiIdDogovoruvac);
                                cmd.Parameters.AddWithValue("@KlientiIdOsigurenik", Input.KlientiIdOsigurenik ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@Kolektivna", Input.Kolektivna);
                                cmd.Parameters.AddWithValue("@KolektivnaNeodredenBrOsigurenici", Input.KolektivnaNeodredenBrOsigurenici);
                                cmd.Parameters.AddWithValue("@NeodredenBrOsigureniciZabeleska", 
                                    (object)Input.NeodredenBrOsigureniciZabeleska ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumVaziOd", (object)Input.DatumVaziOd ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumVaziDo", (object)Input.DatumVaziDo ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaIzdavanje", (object)Input.DatumNaIzdavanje ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@VremetraenjeNaPolisa", (object)Input.VremetraenjeNaPolisa ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@PeriodNaUplata", (object)Input.PeriodNaUplata ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikValutiIdValuta", Input.SifrarnikValutiIdValuta);
                                cmd.Parameters.AddWithValue("@KlientiIdSorabotnik", (object)Input.KlientiIdSorabotnik ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Faktoring", Input.Faktoring);
                                cmd.Parameters.AddWithValue("@SifrarnikValutiIdFranshizaValuta", 
                                    (object)Input.SifrarnikValutiIdFranshizaValuta ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentFranshiza", (object)Input.ProcentFranshiza ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@FranshizaIznos", Input.FranshizaIznos.HasValue ? (object)Input.FranshizaIznos.Value : DBNull.Value);
                                cmd.Parameters.AddWithValue("@KoregiranaStapkaNaProvizija", (object)Input.KoregiranaStapkaNaProvizija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakjanjeId", Input.SifrarnikNacinNaPlakjanjeId);
                                cmd.Parameters.AddWithValue("@TipNaFaktura", (object)Input.TipNaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaFakturaVlezna", 
                                    (object)Input.BrojNaFakturaVlezna ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaFakturaVlezna", 
                                    (object)Input.DatumNaFakturaVlezna ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaVlezna", 
                                    (object)Input.RokNaPlakjanjeFakturaVlezna ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", 
                                    (object)Input.SifrarnikTipNaPlakanjeId ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka", 
                                    (object)Input.SifrarnikBankiIdBanka ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@GeneriranaFakturaIzlezna", Input.GeneriranaFakturaIzlezna);
                                cmd.Parameters.AddWithValue("@BrojNaFakturaIzlezna", 
                                    (object)Input.BrojNaFakturaIzlezna ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaIzleznaFaktura", 
                                    (object)Input.DatumNaIzleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaIzlezna", 
                                    (object)Input.RokNaPlakjanjeFakturaIzlezna ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Storno", Input.Storno);
                                cmd.Parameters.AddWithValue("@SifrarnikPricinaZaStornoId", 
                                    (object)Input.SifrarnikPricinaZaStornoId ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Zabeleska", (object)Input.Zabeleska ?? DBNull.Value);

                                // Execute and get the new Polisi ID
                                polisaId = Convert.ToInt64(await cmd.ExecuteScalarAsync());
                            }

                            // Insert into PolisiKasko table
                            using (SqlCommand cmd = new SqlCommand(@"
                                INSERT INTO PolisiKasko (
                                    DateCreated, UsernameCreated,
                                    PolisaId, OsigurenaVrednost, Opis, VkupnaPremija,
                                    ProcentNaPopustZaFakturaVoRok, IznosZaPlakjanjeVoRok,
                                    ProcentKomercijalenPopust, ProcentFinansiski,
                                    PremijaZaNaplata)
                                VALUES (
                                    GETDATE(), @UsernameCreated,
                                    @PolisaId, @Vrednost, @SumaNaosiguruvanje,
                                    @OsnovnaPremija, @DopolnitelnaPremija, @VkupnaPremija,
                                    @ProcentNaPopustZaFakturaVoRok, @IznosZaPlakjanjeVoRok,
                                    @ProcentKomercijalenPopust, @ProcentFinansiski,
                                    @PremijaZaNaplata)", connection, transaction))
                            {
                                cmd.Parameters.AddWithValue("@UsernameCreated", username);
                                cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                                cmd.Parameters.AddWithValue("@OsigurenaVrednost", (object)Input.OsigurenaVrednost ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Opis", (object)Input.Opis ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@VkupnaPremija", (object)Input.VkupnaPremija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentNaPopustZaFakturaVoRok", (object)Input.ProcentNaPopustZaFakturaVoRok ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@IznosZaPlakjanjeVoRok", (object)Input.IznosZaPlakjanjeVoRok ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentKomercijalenPopust", (object)Input.ProcentKomercijalenPopust ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentFinansiski", (object)Input.ProcentFinansiski ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@PremijaZaNaplata", (object)Input.PremijaZaNaplata ?? DBNull.Value);

                                await cmd.ExecuteNonQueryAsync();
                            }

                            // Insert into PolisiKlasa4Soobrakajna table
                            using (SqlCommand cmd = new SqlCommand(@"
                                INSERT INTO PolisiKlasa4Soobrakajna (
                                    DateCreated, UsernameCreated, PolisaId,
                                    RegisterskaOznaka, Marka, SifrarnikTipNaVozilo,
                                    KomercijalnaOznaka, Shasija, GodinaNaProizvodstvo,
                                    ZafatninaNaMotorotcm3, SilinaNaMotorotKW, BrojNaSedista,
                                    BojaNaVoziloto, NosivostKG, DatumNaRegistracija,
                                    BrojNaVpisot, DatumNaPrvataRegistracija, PrezimeNazivNaKorisnikot,
                                    Ime, AdresaNaPostojanoZivealiste, EMBNaKorisnikot,
                                    DatumNaPrvaRegistracijaVoRSM, DozvolataJaIzdal, OznakaNaOdobrenie,
                                    BrojNAEUPotvrdaZaSoobraznost, PrezimeNazivNaSopstvenikot, ImeSopstvenik,
                                    AdresaNaPostojanoZivealisteSediste, EMBNaFizickoLiceEMBNaPravnoLice,
                                    KategorijaIVidNaVoziloto, OblikINamenaNaKaroserijata, TipNaMotorot,
                                    VidNaGorivo, BrojNaVrtezi, IdentifikacionenBrojNaMotorot,
                                    MaksimalnaBrzinaKM, OdnosSilinaMasa, MasaNaVoziloto,
                                    NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                                    NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                                    NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                                    BrojNaOski, RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                                    NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka,
                                    Dolzhina, Visina,
                                    NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                                    NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                                    BrojNaMestaZaStoenje, DozvoleniPnevmaticiINaplatki,
                                    BrojNaMestazaLezenje, CO2,
                                    NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                                    StacionarnaBucavost)
                                VALUES (
                                    GETDATE(), @UsernameCreated, @PolisaId,
                                    @RegisterskaOznaka, @Marka, @SifrarnikTipNaVozilo,
                                    @KomercijalnaOznaka, @Shasija, @GodinaNaProizvodstvo,
                                    @ZafatninaNaMotorotcm3, @SilinaNaMotorotKW, @BrojNaSedista,
                                    @BojaNaVoziloto, @NosivostKG, @DatumNaRegistracija,
                                    @BrojNaVpisot, @DatumNaPrvataRegistracija, @PrezimeNazivNaKorisnikot,
                                    @Ime, @AdresaNaPostojanoZivealiste, @EMBNaKorisnikot,
                                    @DatumNaPrvaRegistracijaVoRSM, @DozvolataJaIzdal, @OznakaNaOdobrenie,
                                    @BrojNAEUPotvrdaZaSoobraznost, @PrezimeNazivNaSopstvenikot, @ImeSopstvenik,
                                    @AdresaNaPostojanoZivealisteSediste, @EMBNaFizickoLiceEMBNaPravnoLice,
                                    @KategorijaIVidNaVoziloto, @OblikINamenaNaKaroserijata, @TipNaMotorot,
                                    @VidNaGorivo, @BrojNaVrtezi, @IdentifikacionenBrojNaMotorot,
                                    @MaksimalnaBrzinaKM, @OdnosSilinaMasa, @MasaNaVoziloto,
                                    @NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                                    @NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                                    @NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                                    @BrojNaOski, @RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                                    @NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka,
                                    @Dolzhina, @Visina,
                                    @NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                                    @NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                                    @BrojNaMestaZaStoenje, @DozvoleniPnevmaticiINaplatki,
                                    @BrojNaMestazaLezenje, @CO2,
                                    @NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                                    @StacionarnaBucavost)", connection, transaction))
                            {
                                // Set parameters for PolisiKlasa4Soobrakajna table
                                cmd.Parameters.AddWithValue("@UsernameCreated", username);
                                cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                                cmd.Parameters.AddWithValue("@RegisterskaOznaka", (object)Input.RegisterskaOznaka ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Marka", (object)Input.Marka ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikTipNaVozilo", (object)Input.SifrarnikTipNaVoziloId ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@KomercijalnaOznaka", (object)Input.KomercijalnaOznaka ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Shasija", (object)Input.Shasija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@GodinaNaProizvodstvo", (object)Input.GodinaNaProizvodstvo ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ZafatninaNaMotorotcm3", (object)Input.ZafatninaNaMotorotcm3 ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@SilinaNaMotorotKW", (object)Input.SilinaNaMotorotKW ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaSedista", (object)Input.BrojNaSedista ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BojaNaVoziloto", (object)Input.BojaNaVoziloto ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@NosivostKG", (object)Input.NosivostKG ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaRegistracija", (object)Input.DatumNaRegistracija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaVpisot", (object)Input.BrojNaVpisot ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaPrvataRegistracija", (object)Input.DatumNaPrvataRegistracija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@PrezimeNazivNaKorisnikot", (object)Input.PrezimeNazivNaKorisnikot ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Ime", (object)Input.Ime ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@AdresaNaPostojanoZivealiste", (object)Input.AdresaNaPostojanoZivealiste ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@EMBNaKorisnikot", (object)Input.EMBNaKorisnikot ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaPrvaRegistracijaVoRSM", (object)Input.DatumNaPrvaRegistracijaVoRSM ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DozvolataJaIzdal", (object)Input.DozvolataJaIzdal ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@OznakaNaOdobrenie", (object)Input.OznakaNaOdobrenie ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNAEUPotvrdaZaSoobraznost", (object)Input.BrojNAEUPotvrdaZaSoobraznost ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@PrezimeNazivNaSopstvenikot", (object)Input.PrezimeNazivNaSopstvenikot ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ImeSopstvenik", (object)Input.ImeSopstvenik ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@AdresaNaPostojanoZivealisteSediste", (object)Input.AdresaNaPostojanoZivealisteSediste ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@EMBNaFizickoLiceEMBNaPravnoLice", (object)Input.EMBNaFizickoLiceEMBNaPravnoLice ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@KategorijaIVidNaVoziloto", (object)Input.KategorijaIVidNaVoziloto ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@OblikINamenaNaKaroserijata", (object)Input.OblikINamenaNaKaroserijata ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@TipNaMotorot", (object)Input.TipNaMotorot ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@VidNaGorivo", (object)Input.VidNaGorivo ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaVrtezi", (object)Input.BrojNaVrtezi ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@IdentifikacionenBrojNaMotorot", (object)Input.IdentifikacionenBrojNaMotorot ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@MaksimalnaBrzinaKM", (object)Input.MaksimalnaBrzinaKM ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@OdnosSilinaMasa", (object)Input.OdnosSilinaMasa ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@MasaNaVoziloto", (object)Input.MasaNaVoziloto ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG", (object)Input.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG", (object)Input.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG", (object)Input.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaOski", (object)Input.BrojNaOski ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka", (object)Input.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka", (object)Input.NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Dolzhina", (object)Input.Dolzhina ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Visina", (object)Input.Visina ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG", (object)Input.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG", (object)Input.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaMestaZaStoenje", (object)Input.BrojNaMestaZaStoenje ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DozvoleniPnevmaticiINaplatki", (object)Input.DozvoleniPnevmaticiINaplatki ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaMestazaLezenje", (object)Input.BrojNaMestazaLezenje ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@CO2", (object)Input.CO2 ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka", (object)Input.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@StacionarnaBucavost", (object)Input.StacionarnaBucavost ?? DBNull.Value);

                                await cmd.ExecuteNonQueryAsync();
                            }

                            // After successful inserts and before committing transaction,
                            // call the stored procedure
                            using (SqlCommand cmd = new SqlCommand("GenerirajRatiPolisa", connection, transaction))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                                cmd.Parameters.AddWithValue("@SifrarnikValutiId", Input.SifrarnikValutiIdValuta);
                                cmd.Parameters.AddWithValue("@DatumNaIzdavanje", 
                                    Input.DatumNaIzdavanje ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakanjeId", 
                                    Input.SifrarnikNacinNaPlakjanjeId);
                                cmd.Parameters.AddWithValue("@Premija", 
                                    Input.VkupnaPremija ?? (object)DBNull.Value);

                                await cmd.ExecuteNonQueryAsync();
                            }

                            transaction.Commit();
                            TempData["SuccessMessage"] = "Полисата е успешно зачувана.";
                            return new JsonResult(new { 
                                success = true, 
                                redirectUrl = "/Polisi/ListaPolisi"
                            });
                        }
                        catch (Exception)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Се случи грешка при зачувување на полисата:\n";
                errorMessage += $"Error: {ex.Message}\n";
                
                if (ex is SqlException sqlEx)
                {
                    errorMessage += $"SQL Error Number: {sqlEx.Number}\n";
                    errorMessage += $"SQL State: {sqlEx.State}\n";
                    errorMessage += $"SQL Procedure: {sqlEx.Procedure}\n";
                    errorMessage += $"Line Number: {sqlEx.LineNumber}\n";
                }
                
                if (ex.InnerException != null)
                {
                    errorMessage += $"Inner Exception: {ex.InnerException.Message}\n";
                }
                
                // Log the error (you might want to use proper logging here)
                System.Diagnostics.Debug.WriteLine(errorMessage);
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");

                return new JsonResult(new { 
                    success = false, 
                    errorMessage = errorMessage.Replace("\n", "<br/>") 
                });
            }
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetSearchSorabotniciAsync(string mb)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            // Check for duplicate policy number
            if (!string.IsNullOrEmpty(Input.BrojNaPolisa))
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        SELECT COUNT(*) 
                        FROM Polisi 
                        WHERE BrojNaPolisa = @BrojNaPolisa 
                        AND (Storno = 0 OR Storno IS NULL)", connection))
                    {
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaPolisa);
                        int count = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                        if (count > 0)
                        {
                            return new JsonResult(new { 
                                success = false, 
                                errorMessage = "Веќе постои полиса со овој број која не е сторнирана!" 
                            });
                        }
                    }
                }
            }
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE (KlientSorabotnik = 1 or KlientVraboten = 1)
                        AND (MB LIKE @Search + '%'
                        OR EDB LIKE @Search + '%'
                        OR EMBG LIKE @Search + '%'
                        OR Naziv LIKE '%' + @Search + '%'
                        OR Ime LIKE '%' + @Search + '%'
                        OR Prezime LIKE '%' + @Search + '%')
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }
    }
} 